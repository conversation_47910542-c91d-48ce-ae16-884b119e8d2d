using LibraryManagementSystem.DAL.Models;
using LibraryManagementSystem.DAL.Models.DTOs;
using LibraryManagementSystem.DAL.Repositories;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace LibraryManagementSystem.UI.Pages.Books
{
    /// <summary>
    /// نموذج صفحة البحث عن الكتب
    /// Books search page model
    /// </summary>
    public class IndexModel : PageModel
    {
        private readonly IBookRepository _bookRepository;
        private readonly ILogger<IndexModel> _logger;

        /// <summary>
        /// منشئ نموذج الصفحة
        /// Page model constructor
        /// </summary>
        public IndexModel(IBookRepository bookRepository, ILogger<IndexModel> logger)
        {
            _bookRepository = bookRepository ?? throw new ArgumentNullException(nameof(bookRepository));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// معايير البحث الحالية
        /// Current search criteria
        /// </summary>
        [BindProperty(SupportsGet = true)]
        public BookSearchDto SearchCriteria { get; set; } = new BookSearchDto();

        /// <summary>
        /// نتائج البحث
        /// Search results
        /// </summary>
        public PagedResult<Book>? SearchResults { get; set; }

        /// <summary>
        /// رسالة الخطأ في حالة حدوث مشكلة
        /// Error message in case of issues
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// معالج طلب GET - تحميل الصفحة والبحث
        /// GET request handler - load page and search
        /// </summary>
        public async Task<IActionResult> OnGetAsync(
            string? title,
            string? author,
            string? isbn,
            string? genre,
            bool? isAvailable,
            int pageNumber = 1,
            int pageSize = 10,
            string sortBy = "Title",
            bool sortDescending = false)
        {
            try
            {
                // تعيين معايير البحث
                // Set search criteria
                SearchCriteria = new BookSearchDto
                {
                    Title = title,
                    Author = author,
                    ISBN = isbn,
                    Genre = genre,
                    IsAvailable = isAvailable,
                    PageNumber = Math.Max(1, pageNumber),
                    PageSize = Math.Max(1, Math.Min(50, pageSize)), // الحد الأقصى 50 عنصر في الصفحة
                    SortBy = sortBy,
                    SortDescending = sortDescending
                };

                // تسجيل معايير البحث
                // Log search criteria
                _logger.LogDebug("البحث عن الكتب بالمعايير: العنوان={Title}, المؤلف={Author}, ISBN={ISBN}, النوع={Genre}, متاح={IsAvailable} - " +
                    "Searching books with criteria: Title={Title}, Author={Author}, ISBN={ISBN}, Genre={Genre}, Available={IsAvailable}",
                    title, author, isbn, genre, isAvailable);

                // تنفيذ البحث
                // Execute search
                SearchResults = await _bookRepository.SearchAsync(SearchCriteria);

                // تسجيل النتائج
                // Log results
                _logger.LogInformation("تم العثور على {Count} كتاب من أصل {Total} - Found {Count} books out of {Total}",
                    SearchResults.Items.Count, SearchResults.TotalCount);

                // إضافة رسالة معلوماتية إذا لم يتم العثور على نتائج
                // Add informational message if no results found
                if (SearchResults.TotalCount == 0 && HasSearchCriteria())
                {
                    TempData["InfoMessage"] = "لم يتم العثور على كتب تطابق معايير البحث المحددة. جرب تعديل معايير البحث.";
                }

                return Page();
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ
                // Log error
                _logger.LogError(ex, "خطأ في البحث عن الكتب - Error searching for books");
                
                // عرض رسالة خطأ للمستخدم
                // Display error message to user
                ErrorMessage = "حدث خطأ أثناء البحث عن الكتب. يرجى المحاولة مرة أخرى.";
                TempData["ErrorMessage"] = ErrorMessage;
                
                // إرجاع نتائج فارغة
                // Return empty results
                SearchResults = new PagedResult<Book>
                {
                    Items = new List<Book>(),
                    TotalCount = 0,
                    PageNumber = SearchCriteria.PageNumber,
                    PageSize = SearchCriteria.PageSize
                };

                return Page();
            }
        }

        /// <summary>
        /// معالج طلب POST - تنفيذ البحث الجديد
        /// POST request handler - execute new search
        /// </summary>
        public async Task<IActionResult> OnPostAsync()
        {
            try
            {
                // التحقق من صحة النموذج
                // Validate model
                if (!ModelState.IsValid)
                {
                    TempData["ErrorMessage"] = "يرجى التحقق من صحة البيانات المدخلة.";
                    return Page();
                }

                // إعادة توجيه إلى GET مع معايير البحث
                // Redirect to GET with search criteria
                var queryParams = new List<string>();
                
                if (!string.IsNullOrWhiteSpace(SearchCriteria.Title))
                    queryParams.Add($"title={Uri.EscapeDataString(SearchCriteria.Title)}");
                
                if (!string.IsNullOrWhiteSpace(SearchCriteria.Author))
                    queryParams.Add($"author={Uri.EscapeDataString(SearchCriteria.Author)}");
                
                if (!string.IsNullOrWhiteSpace(SearchCriteria.ISBN))
                    queryParams.Add($"isbn={Uri.EscapeDataString(SearchCriteria.ISBN)}");
                
                if (!string.IsNullOrWhiteSpace(SearchCriteria.Genre))
                    queryParams.Add($"genre={Uri.EscapeDataString(SearchCriteria.Genre)}");
                
                if (SearchCriteria.IsAvailable.HasValue)
                    queryParams.Add($"isAvailable={SearchCriteria.IsAvailable.Value}");
                
                queryParams.Add($"pageNumber={SearchCriteria.PageNumber}");
                queryParams.Add($"pageSize={SearchCriteria.PageSize}");
                queryParams.Add($"sortBy={SearchCriteria.SortBy}");
                queryParams.Add($"sortDescending={SearchCriteria.SortDescending}");

                var queryString = string.Join("&", queryParams);
                var redirectUrl = $"/Books?{queryString}";

                return Redirect(redirectUrl);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في معالجة طلب البحث - Error processing search request");
                TempData["ErrorMessage"] = "حدث خطأ أثناء معالجة طلب البحث. يرجى المحاولة مرة أخرى.";
                return Page();
            }
        }

        /// <summary>
        /// التحقق من وجود معايير بحث
        /// Check if search criteria exist
        /// </summary>
        /// <returns>true إذا كانت هناك معايير بحث</returns>
        private bool HasSearchCriteria()
        {
            return !string.IsNullOrWhiteSpace(SearchCriteria.Title) ||
                   !string.IsNullOrWhiteSpace(SearchCriteria.Author) ||
                   !string.IsNullOrWhiteSpace(SearchCriteria.ISBN) ||
                   !string.IsNullOrWhiteSpace(SearchCriteria.Genre) ||
                   SearchCriteria.IsAvailable.HasValue;
        }

        /// <summary>
        /// الحصول على رابط الصفحة مع رقم صفحة محدد
        /// Get page URL with specific page number
        /// </summary>
        /// <param name="pageNumber">رقم الصفحة</param>
        /// <returns>رابط الصفحة</returns>
        public string GetPageUrl(int pageNumber)
        {
            var queryParams = new List<string>();
            
            if (!string.IsNullOrWhiteSpace(SearchCriteria.Title))
                queryParams.Add($"title={Uri.EscapeDataString(SearchCriteria.Title)}");
            
            if (!string.IsNullOrWhiteSpace(SearchCriteria.Author))
                queryParams.Add($"author={Uri.EscapeDataString(SearchCriteria.Author)}");
            
            if (!string.IsNullOrWhiteSpace(SearchCriteria.ISBN))
                queryParams.Add($"isbn={Uri.EscapeDataString(SearchCriteria.ISBN)}");
            
            if (!string.IsNullOrWhiteSpace(SearchCriteria.Genre))
                queryParams.Add($"genre={Uri.EscapeDataString(SearchCriteria.Genre)}");
            
            if (SearchCriteria.IsAvailable.HasValue)
                queryParams.Add($"isAvailable={SearchCriteria.IsAvailable.Value}");
            
            queryParams.Add($"pageNumber={pageNumber}");
            queryParams.Add($"pageSize={SearchCriteria.PageSize}");
            queryParams.Add($"sortBy={SearchCriteria.SortBy}");
            queryParams.Add($"sortDescending={SearchCriteria.SortDescending}");

            var queryString = string.Join("&", queryParams);
            return $"/Books?{queryString}";
        }
    }
}
