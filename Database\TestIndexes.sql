-- Test Script for Library Management System Indexes
-- سكريبت اختبار فهارس نظام إدارة المكتبة

USE LibraryManagementSystem;
GO

PRINT 'بدء اختبار الفهارس والأداء - Starting index and performance testing...';
GO

-- اختبار 1: التحقق من وجود الفهارس
-- Test 1: Verify index existence
PRINT '=== اختبار وجود الفهارس - Index Existence Test ===';

SELECT 
    OBJECT_NAME(i.object_id) AS TableName,
    i.name AS IndexName,
    i.type_desc AS IndexType,
    i.is_unique AS IsUnique,
    CASE 
        WHEN i.has_filter = 1 THEN 'Yes' 
        ELSE 'No' 
    END AS HasFilter
FROM sys.indexes i
WHERE OBJECT_NAME(i.object_id) IN ('Books', 'Users', 'Borrowings')
    AND i.type > 0  -- Exclude heap
    AND i.name LIKE 'IX_%'  -- Only our custom indexes
ORDER BY OBJECT_NAME(i.object_id), i.name;

-- اختبار 2: اختبار أداء البحث في الكتب
-- Test 2: Book search performance test
PRINT '';
PRINT '=== اختبار أداء البحث في الكتب - Book Search Performance Test ===';

-- تشغيل خطة التنفيذ
SET STATISTICS IO ON;
SET STATISTICS TIME ON;

-- البحث بالعنوان
PRINT 'البحث بالعنوان - Title search:';
SELECT BookId, Title, Author, AvailableCopies
FROM Books 
WHERE Title LIKE '%Harry%'
ORDER BY Title;

-- البحث بالمؤلف
PRINT 'البحث بالمؤلف - Author search:';
SELECT BookId, Title, Author, AvailableCopies
FROM Books 
WHERE Author LIKE '%Tolkien%'
ORDER BY Author;

-- البحث بالنوع
PRINT 'البحث بالنوع - Genre search:';
SELECT BookId, Title, Author, Genre, AvailableCopies
FROM Books 
WHERE Genre = 'Fantasy'
ORDER BY Title;

-- البحث في الكتب المتاحة
PRINT 'البحث في الكتب المتاحة - Available books search:';
SELECT BookId, Title, Author, AvailableCopies
FROM Books 
WHERE AvailableCopies > 0
ORDER BY Title;

SET STATISTICS IO OFF;
SET STATISTICS TIME OFF;

-- اختبار 3: اختبار أداء استعلامات الاستعارات
-- Test 3: Borrowing queries performance test
PRINT '';
PRINT '=== اختبار أداء استعلامات الاستعارات - Borrowing Queries Performance Test ===';

SET STATISTICS IO ON;

-- الاستعارات النشطة لمستخدم معين
PRINT 'الاستعارات النشطة لمستخدم - Active borrowings for user:';
SELECT b.BorrowingId, bk.Title, b.BorrowDate, b.DueDate
FROM Borrowings b
INNER JOIN Books bk ON b.BookId = bk.BookId
WHERE b.UserId = 1 AND b.IsReturned = 0
ORDER BY b.BorrowDate DESC;

-- الكتب المتأخرة
PRINT 'الكتب المتأخرة - Overdue books:';
SELECT b.BorrowingId, u.FirstName + ' ' + u.LastName AS UserName, 
       bk.Title, b.BorrowDate, b.DueDate,
       DATEDIFF(day, b.DueDate, GETDATE()) AS DaysOverdue
FROM Borrowings b
INNER JOIN Users u ON b.UserId = u.UserId
INNER JOIN Books bk ON b.BookId = bk.BookId
WHERE b.IsReturned = 0 AND b.DueDate < GETDATE()
ORDER BY b.DueDate;

-- إحصائيات الاستعارات الشهرية
PRINT 'إحصائيات الاستعارات الشهرية - Monthly borrowing statistics:';
SELECT 
    YEAR(BorrowDate) AS Year,
    MONTH(BorrowDate) AS Month,
    COUNT(*) AS TotalBorrowings,
    COUNT(CASE WHEN IsReturned = 1 THEN 1 END) AS ReturnedBooks,
    COUNT(CASE WHEN IsReturned = 0 THEN 1 END) AS ActiveBorrowings
FROM Borrowings
WHERE BorrowDate >= DATEADD(month, -6, GETDATE())
GROUP BY YEAR(BorrowDate), MONTH(BorrowDate)
ORDER BY Year DESC, Month DESC;

SET STATISTICS IO OFF;

-- اختبار 4: اختبار استعلامات المستخدمين
-- Test 4: User queries test
PRINT '';
PRINT '=== اختبار استعلامات المستخدمين - User Queries Test ===';

-- البحث بالاسم
PRINT 'البحث بالاسم - Name search:';
SELECT UserId, FirstName, LastName, Email, MembershipDate
FROM Users
WHERE FirstName LIKE '%John%' OR LastName LIKE '%John%'
ORDER BY LastName, FirstName;

-- المستخدمون النشطون
PRINT 'المستخدمون النشطون - Active users:';
SELECT UserId, FirstName, LastName, Email, MembershipDate
FROM Users
WHERE IsActive = 1
ORDER BY MembershipDate DESC;

-- اختبار 5: اختبار الاستعلامات المعقدة
-- Test 5: Complex queries test
PRINT '';
PRINT '=== اختبار الاستعلامات المعقدة - Complex Queries Test ===';

-- أكثر الكتب استعارة
PRINT 'أكثر الكتب استعارة - Most borrowed books:';
SELECT TOP 10
    b.BookId,
    b.Title,
    b.Author,
    COUNT(br.BorrowingId) AS BorrowCount,
    b.TotalCopies,
    b.AvailableCopies
FROM Books b
LEFT JOIN Borrowings br ON b.BookId = br.BookId
GROUP BY b.BookId, b.Title, b.Author, b.TotalCopies, b.AvailableCopies
ORDER BY BorrowCount DESC;

-- المستخدمون الأكثر نشاطاً
PRINT 'المستخدمون الأكثر نشاطاً - Most active users:';
SELECT TOP 10
    u.UserId,
    u.FirstName + ' ' + u.LastName AS UserName,
    u.Email,
    COUNT(b.BorrowingId) AS TotalBorrowings,
    COUNT(CASE WHEN b.IsReturned = 0 THEN 1 END) AS ActiveBorrowings
FROM Users u
LEFT JOIN Borrowings b ON u.UserId = b.UserId
WHERE u.IsActive = 1
GROUP BY u.UserId, u.FirstName, u.LastName, u.Email
ORDER BY TotalBorrowings DESC;

-- اختبار 6: تحليل استخدام الفهارس
-- Test 6: Index usage analysis
PRINT '';
PRINT '=== تحليل استخدام الفهارس - Index Usage Analysis ===';

SELECT 
    OBJECT_NAME(s.object_id) AS TableName,
    i.name AS IndexName,
    s.user_seeks AS Seeks,
    s.user_scans AS Scans,
    s.user_lookups AS Lookups,
    s.user_updates AS Updates,
    s.user_seeks + s.user_scans + s.user_lookups AS TotalReads,
    CASE 
        WHEN s.user_updates > 0 
        THEN CAST((s.user_seeks + s.user_scans + s.user_lookups) AS FLOAT) / s.user_updates
        ELSE 0 
    END AS ReadWriteRatio
FROM sys.dm_db_index_usage_stats s
INNER JOIN sys.indexes i ON s.object_id = i.object_id AND s.index_id = i.index_id
WHERE OBJECT_NAME(s.object_id) IN ('Books', 'Users', 'Borrowings')
    AND i.name LIKE 'IX_%'
ORDER BY TotalReads DESC;

-- اختبار 7: حجم الفهارس
-- Test 7: Index size analysis
PRINT '';
PRINT '=== تحليل حجم الفهارس - Index Size Analysis ===';

SELECT 
    OBJECT_NAME(i.object_id) AS TableName,
    i.name AS IndexName,
    SUM(s.used_page_count) * 8 AS IndexSizeKB,
    SUM(s.row_count) AS RowCount
FROM sys.dm_db_partition_stats s
INNER JOIN sys.indexes i ON s.object_id = i.object_id AND s.index_id = i.index_id
WHERE OBJECT_NAME(i.object_id) IN ('Books', 'Users', 'Borrowings')
    AND i.name LIKE 'IX_%'
GROUP BY i.object_id, i.name
ORDER BY IndexSizeKB DESC;

PRINT '';
PRINT 'تم الانتهاء من اختبار الفهارس بنجاح! - Index testing completed successfully!';
PRINT 'راجع النتائج أعلاه لتقييم أداء الفهارس - Review the results above to evaluate index performance';
GO
