@page
@model LibraryManagementSystem.UI.Pages.Books.IndexModel
@{
    ViewData["Title"] = "البحث عن الكتب";
}

<div class="row">
    <div class="col-12">
        <h2 class="mb-4">
            <i class="fas fa-search me-2"></i>
            البحث عن الكتب
        </h2>
    </div>
</div>

<!-- نموذج البحث - Search form -->
<div class="search-container">
    <form method="get" class="row g-3">
        <div class="col-md-3">
            <label for="title" class="form-label">عنوان الكتاب</label>
            <input type="text" class="form-control" id="title" name="title" 
                   value="@Model.SearchCriteria.Title" placeholder="ادخل عنوان الكتاب">
        </div>
        
        <div class="col-md-3">
            <label for="author" class="form-label">المؤلف</label>
            <input type="text" class="form-control" id="author" name="author" 
                   value="@Model.SearchCriteria.Author" placeholder="ادخل اسم المؤلف">
        </div>
        
        <div class="col-md-2">
            <label for="isbn" class="form-label">الرقم المعياري</label>
            <input type="text" class="form-control" id="isbn" name="isbn" 
                   value="@Model.SearchCriteria.ISBN" placeholder="ISBN">
        </div>
        
        <div class="col-md-2">
            <label for="genre" class="form-label">النوع</label>
            <select class="form-select" id="genre" name="genre">
                <option value="">جميع الأنواع</option>
                <option value="Fiction" selected="@(Model.SearchCriteria.Genre == "Fiction")">خيال</option>
                <option value="Science Fiction" selected="@(Model.SearchCriteria.Genre == "Science Fiction")">خيال علمي</option>
                <option value="Mystery" selected="@(Model.SearchCriteria.Genre == "Mystery")">غموض</option>
                <option value="Romance" selected="@(Model.SearchCriteria.Genre == "Romance")">رومانسي</option>
                <option value="Fantasy" selected="@(Model.SearchCriteria.Genre == "Fantasy")">فانتازيا</option>
                <option value="Historical Fiction" selected="@(Model.SearchCriteria.Genre == "Historical Fiction")">خيال تاريخي</option>
                <option value="Biography" selected="@(Model.SearchCriteria.Genre == "Biography")">سيرة ذاتية</option>
                <option value="Non-Fiction" selected="@(Model.SearchCriteria.Genre == "Non-Fiction")">غير خيالي</option>
            </select>
        </div>
        
        <div class="col-md-2">
            <label for="isAvailable" class="form-label">التوفر</label>
            <select class="form-select" id="isAvailable" name="isAvailable">
                <option value="">الكل</option>
                <option value="true" selected="@(Model.SearchCriteria.IsAvailable == true)">متاح</option>
                <option value="false" selected="@(Model.SearchCriteria.IsAvailable == false)">غير متاح</option>
            </select>
        </div>
        
        <div class="col-12">
            <button type="submit" class="btn btn-primary me-2">
                <i class="fas fa-search me-1"></i>
                بحث
            </button>
            <a href="/Books" class="btn btn-outline-secondary">
                <i class="fas fa-times me-1"></i>
                مسح
            </a>
        </div>
    </form>
</div>

<!-- نتائج البحث - Search results -->
@if (Model.SearchResults != null && Model.SearchResults.Items.Any())
{
    <div class="row mb-3">
        <div class="col-md-6">
            <h4>نتائج البحث</h4>
            <p class="text-muted">
                عرض @Model.SearchResults.Items.Count من أصل @Model.SearchResults.TotalCount كتاب
                (الصفحة @Model.SearchResults.PageNumber من @Model.SearchResults.TotalPages)
            </p>
        </div>
        <div class="col-md-6 text-end">
            <div class="btn-group" role="group">
                <input type="radio" class="btn-check" name="sortBy" id="sortTitle" value="Title" 
                       checked="@(Model.SearchCriteria.SortBy == "Title")" onchange="updateSort(this)">
                <label class="btn btn-outline-primary" for="sortTitle">ترتيب بالعنوان</label>
                
                <input type="radio" class="btn-check" name="sortBy" id="sortAuthor" value="Author" 
                       checked="@(Model.SearchCriteria.SortBy == "Author")" onchange="updateSort(this)">
                <label class="btn btn-outline-primary" for="sortAuthor">ترتيب بالمؤلف</label>
                
                <input type="radio" class="btn-check" name="sortBy" id="sortYear" value="PublicationYear" 
                       checked="@(Model.SearchCriteria.SortBy == "PublicationYear")" onchange="updateSort(this)">
                <label class="btn btn-outline-primary" for="sortYear">ترتيب بالسنة</label>
            </div>
        </div>
    </div>

    <!-- جدول النتائج - Results table -->
    <div class="card">
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th>العنوان</th>
                            <th>المؤلف</th>
                            <th>النوع</th>
                            <th>سنة النشر</th>
                            <th>التوفر</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var book in Model.SearchResults.Items)
                        {
                            <tr>
                                <td>
                                    <strong>@book.Title</strong>
                                    @if (!string.IsNullOrEmpty(book.Description))
                                    {
                                        <br><small class="text-muted">@book.Description.Substring(0, Math.Min(100, book.Description.Length))...</small>
                                    }
                                </td>
                                <td>@book.Author</td>
                                <td>
                                    @if (!string.IsNullOrEmpty(book.Genre))
                                    {
                                        <span class="badge bg-secondary">@book.Genre</span>
                                    }
                                </td>
                                <td>@book.PublicationYear</td>
                                <td>
                                    @if (book.IsAvailable)
                                    {
                                        <span class="badge bg-success">
                                            <i class="fas fa-check me-1"></i>
                                            متاح (@book.AvailableCopies من @book.TotalCopies)
                                        </span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-danger">
                                            <i class="fas fa-times me-1"></i>
                                            غير متاح
                                        </span>
                                    }
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="/Books/Details/@book.BookId" class="btn btn-outline-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        @if (book.IsAvailable)
                                        {
                                            <button type="button" class="btn btn-outline-primary" 
                                                    onclick="borrowBook(@book.BookId, '@book.Title')">
                                                <i class="fas fa-book"></i>
                                            </button>
                                        }
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- التنقل بين الصفحات - Pagination -->
    @if (Model.SearchResults.TotalPages > 1)
    {
        <nav class="mt-4">
            <ul class="pagination justify-content-center">
                @if (Model.SearchResults.HasPreviousPage)
                {
                    <li class="page-item">
                        <a class="page-link" href="@GetPageUrl(Model.SearchResults.PageNumber - 1)">السابق</a>
                    </li>
                }
                
                @for (int i = Math.Max(1, Model.SearchResults.PageNumber - 2); 
                      i <= Math.Min(Model.SearchResults.TotalPages, Model.SearchResults.PageNumber + 2); 
                      i++)
                {
                    <li class="page-item @(i == Model.SearchResults.PageNumber ? "active" : "")">
                        <a class="page-link" href="@GetPageUrl(i)">@i</a>
                    </li>
                }
                
                @if (Model.SearchResults.HasNextPage)
                {
                    <li class="page-item">
                        <a class="page-link" href="@GetPageUrl(Model.SearchResults.PageNumber + 1)">التالي</a>
                    </li>
                }
            </ul>
        </nav>
    }
}
else if (Model.SearchResults != null)
{
    <div class="alert alert-info text-center">
        <i class="fas fa-info-circle me-2"></i>
        لم يتم العثور على كتب تطابق معايير البحث المحددة.
    </div>
}

@functions {
    private string GetPageUrl(int pageNumber)
    {
        var queryString = Request.QueryString.ToString();
        if (queryString.Contains("pageNumber="))
        {
            queryString = System.Text.RegularExpressions.Regex.Replace(queryString, @"pageNumber=\d+", $"pageNumber={pageNumber}");
        }
        else
        {
            queryString += (queryString.Contains("?") ? "&" : "?") + $"pageNumber={pageNumber}";
        }
        return Request.Path + queryString;
    }
}

@section Scripts {
    <script>
        // تحديث الترتيب
        // Update sorting
        function updateSort(element) {
            const form = document.querySelector('form');
            const sortInput = document.createElement('input');
            sortInput.type = 'hidden';
            sortInput.name = 'sortBy';
            sortInput.value = element.value;
            form.appendChild(sortInput);
            form.submit();
        }
        
        // استعارة كتاب
        // Borrow book
        function borrowBook(bookId, title) {
            if (confirm(`هل تريد استعارة الكتاب: ${title}؟`)) {
                // إرسال طلب الاستعارة
                fetch('/api/borrowings', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        bookId: bookId,
                        userId: 1 // يجب الحصول على معرف المستخدم الحالي
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('تم استعارة الكتاب بنجاح!');
                        location.reload();
                    } else {
                        alert('حدث خطأ في استعارة الكتاب: ' + data.message);
                    }
                })
                .catch(error => {
                    alert('حدث خطأ في الاتصال بالخادم');
                });
            }
        }
    </script>
}
