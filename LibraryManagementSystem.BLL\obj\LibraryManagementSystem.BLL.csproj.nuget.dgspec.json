{"format": 1, "restore": {"c:\\Titan Task\\LibraryManagementSystem\\LibraryManagementSystem.BLL\\LibraryManagementSystem.BLL.csproj": {}}, "projects": {"c:\\Titan Task\\LibraryManagementSystem\\LibraryManagementSystem.BLL\\LibraryManagementSystem.BLL.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "c:\\Titan Task\\LibraryManagementSystem\\LibraryManagementSystem.BLL\\LibraryManagementSystem.BLL.csproj", "projectName": "LibraryManagementSystem.BLL", "projectPath": "c:\\Titan Task\\LibraryManagementSystem\\LibraryManagementSystem.BLL\\LibraryManagementSystem.BLL.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "c:\\Titan Task\\LibraryManagementSystem\\LibraryManagementSystem.BLL\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"c:\\Titan Task\\LibraryManagementSystem\\LibraryManagementSystem.DAL\\LibraryManagementSystem.DAL.csproj": {"projectPath": "c:\\Titan Task\\LibraryManagementSystem\\LibraryManagementSystem.DAL\\LibraryManagementSystem.DAL.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "c:\\Titan Task\\LibraryManagementSystem\\LibraryManagementSystem.DAL\\LibraryManagementSystem.DAL.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "c:\\Titan Task\\LibraryManagementSystem\\LibraryManagementSystem.DAL\\LibraryManagementSystem.DAL.csproj", "projectName": "LibraryManagementSystem.DAL", "projectPath": "c:\\Titan Task\\LibraryManagementSystem\\LibraryManagementSystem.DAL\\LibraryManagementSystem.DAL.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "c:\\Titan Task\\LibraryManagementSystem\\LibraryManagementSystem.DAL\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.Data.SqlClient": {"target": "Package", "version": "[6.1.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}